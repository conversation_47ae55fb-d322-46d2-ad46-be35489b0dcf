"""Tests for the `main` module."""

import json
from pathlib import Path

import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from pytest_mock import Mo<PERSON><PERSON><PERSON><PERSON>

from olympus_common import defaults
from tests.olympus_common.mocked_db import MockedDBModule
from tests.olympus_common.test_core import compare_outputs
from tests.utils import filter_outputs, remove_middleware


@pytest.mark.parametrize("input_file", ["01.json"])
def test_main(mocker: MockerFixture, input_file: str):
    """Test the webhook endpoint with test data."""
    from mon_big_data.main import app

    remove_middleware(app, "AuthMiddleware")

    mocked_database_writer = mocker.patch("mon_big_data.main.DatabaseWriter")
    mocker.patch("mon_big_data.dd.db", new=MockedDBModule())

    client = TestClient(app)
    url = "http://127.0.0.1:8000/webhook"
    path = Path(__file__).parent / "data" / input_file
    data = json.loads(path.read_text())
    testdata = data.get("data", [])

    response = client.post(url, json=data)
    assert response.status_code == 200
    assert mocked_database_writer.return_value.success.called

    # Compare response data with expected output
    expected_outputs = [item["output"] for item in testdata]
    assert response.json() == expected_outputs


def test_mon_big_data_transformation(mocker: MockerFixture):
    """Test the mon_big_data transformation logic.

    This test specifically tests the transformation logic used by the mon_big_data FastAPI webhook service.
    It follows the same pattern as test_service_runner but uses the defaults.transform function directly since
    mon_big_data is a FastAPI service rather than a standard KafkaReader/DatabaseWriter service.
    """

    from freezegun import freeze_time

    from mon_big_data import dd
    from mon_big_data.config import config
    from tests.olympus_common.test_core import FROZEN_TIME

    mocker.patch("mon_big_data.dd.db", new=MockedDBModule())

    # Extract the data from the test file
    path = Path(__file__).parent.parent / "olympus_common" / "data" / "mon_big_data.json"
    testcontent = json.loads(path.read_text())
    testdata: list[dict[str, dict]] = testcontent["data"]
    filter_outputs(testdata, convert_dates=True)

    # Extract inputs from the test data
    input_: list[dict] = [item["input"] for item in testdata]

    with freeze_time(FROZEN_TIME):
        transformed_data = defaults.transform(input_, config, dd.run, None)

    compare_outputs(transformed_data, testdata)
