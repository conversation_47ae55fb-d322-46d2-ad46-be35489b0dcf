"""Tests for the `main` module."""

import json
from pathlib import Path

import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from pytest_mock import Mocker<PERSON><PERSON><PERSON>

from tests.olympus_common.mocked_db import MockedDBModule
from tests.utils import remove_middleware


@pytest.mark.parametrize("input_file", ["01.json"])
def test_main(mocker: MockerFixture, input_file: str):
    """Test the webhook endpoint with test data."""
    from mon_big_data.main import app

    remove_middleware(app, "AuthMiddleware")

    mocked_database_writer = mocker.patch("mon_big_data.main.DatabaseWriter")
    mocker.patch("mon_big_data.dd.db", new=MockedDBModule())

    client = TestClient(app)
    url = "http://127.0.0.1:8000/webhook"
    path = Path(__file__).parent / "data" / input_file
    test_content = json.loads(path.read_text())
    testdata = test_content.get("data", [])

    # Extract input data and format it for the webhook endpoint
    # The webhook expects PrometheusAlert format with "alerts" array
    alerts = [item["input"] for item in testdata]
    webhook_payload = {"alerts": alerts}

    response = client.post(url, json=webhook_payload)
    assert response.status_code == 200
    assert mocked_database_writer.return_value.success.called


@pytest.mark.parametrize("input_file", ["mon_big_data.json"])
def test_mon_big_data_transformation(mocker: MockerFixture, input_file: str):
    """Test the mon_big_data transformation logic.

    This test specifically tests the transformation logic used by the mon_big_data FastAPI webhook service.
    It follows the same pattern as test_service_runner but uses the defaults.transform function directly since
    mon_big_data is a FastAPI service rather than a standard KafkaReader/DatabaseWriter service.

    Args:
        mocker: pytest-mock fixture for mocking
        input_file: Name of the JSON test data file to load from tests/olympus_common/data/
    """
    from freezegun import freeze_time

    from mon_big_data import dd
    from mon_big_data.config import config
    from olympus_common import defaults
    from tests.olympus_common.test_core import FROZEN_TIME, compare_outputs
    from tests.utils import filter_outputs

    mocker.patch("mon_big_data.dd.db", new=MockedDBModule())

    # Load test data from JSON file following the same pattern as test_service_runner.
    path = Path(__file__).parent.parent / "olympus_common" / "data" / input_file
    testcontent = json.loads(path.read_text())
    testdata: list[dict[str, dict]] = testcontent["data"]
    filter_outputs(testdata, convert_dates=True)

    # Extract inputs from the test data
    input_: list[dict] = [item["input"] for item in testdata]

    # Freeze time to match expected test timestamps
    with freeze_time(FROZEN_TIME):
        transformed_data = defaults.transform(input_, config, dd.run, None)

    compare_outputs(transformed_data, testdata)
