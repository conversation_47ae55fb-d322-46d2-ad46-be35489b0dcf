"""Tests for the `main` module."""

import json
from pathlib import Path
from typing import Any

from fastapi.testclient import Test<PERSON>lient
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from olympus_common import defaults
from tests.olympus_common.mocked_db import MockedDBModule
from tests.olympus_common.test_core import compare_outputs
from tests.utils import filter_outputs, remove_middleware


def test_main(mocker: MockerFixture):
    """Placeholder test."""
    from mon_big_data.main import app

    remove_middleware(app, "AuthMiddleware")

    mocked_database_writer = mocker.patch("mon_big_data.main.DatabaseWriter")
    mocker.patch("mon_big_data.dd.db", new=MockedDBModule())

    client = TestClient(app)
    url = "http://127.0.0.1:8000/webhook"
    data: Any = {
        "receiver": "olympus-acc",
        "status": "firing",
        "alerts": [
            {
                "status": "firing",
                "labels": {
                    "acode": "A2066",
                    "alertname": "prometheus_healthcheck",
                    "category": "healthcheck",
                    "ci": "iictmismlv013",
                    "env": "prod",
                    "instance": "iictmismlv013.msnet.railb.be",
                    "job": "prometheus",
                    "metricname": "prometheus_healthcheck",
                    "metrictype": "/Application/",
                    "node": "iictmismlv013.msnet.railb.be",
                    "optic": "enabled",
                    "server": "iictmismlv013",
                    "severity": "info",
                    "source": "prometheus",
                },
                "annotations": {"summary": "Healthcheck Instance iictmismlv013.msnet.railb.be is ok"},
                "startsAt": "2023-11-27T12:49:33.674Z",
                "endsAt": "0001-01-01T00:00:00Z",
                "generatorURL": "https://iictmismlv013.msnet.railb.be/prometheus/graph?",
                "fingerprint": "0eedc3cd100922a7",
            },
        ],
        "groupLabels": {"instance": "iictmismlv013.msnet.railb.be"},
        "commonLabels": {
            "acode": "A2066",
            "alertname": "prometheus_healthcheck",
            "category": "healthcheck",
            "ci": "iictmismlv013",
            "env": "prod",
            "instance": "iictmismlv013.msnet.railb.be",
            "job": "prometheus",
            "metricname": "prometheus_healthcheck",
            "metrictype": "/Application/",
            "node": "iictmismlv013.msnet.railb.be",
            "optic": "enabled",
            "server": "iictmismlv013",
            "severity": "info",
            "source": "prometheus",
        },
        "commonAnnotations": {"summary": "Healthcheck Instance iictmismlv013.msnet.railb.be is ok"},
        "externalURL": "https://iictmismlv013.msnet.railb.be/alertmanager",
        "version": "4",
        "groupKey": '{}/{env="prod",optic="enabled"}:{instance="iictmismlv013.msnet.railb.be"}',
        "truncatedAlerts": 0,
    }

    response = client.post(url, json=data)
    assert response.status_code == 200
    assert mocked_database_writer.return_value.success.called


def test_mon_big_data_transformation(mocker: MockerFixture):
    """Test the mon_big_data transformation logic.

    This test specifically tests the transformation logic used by the mon_big_data FastAPI webhook service.
    It follows the same pattern as test_service_runner but uses the defaults.transform function directly since
    mon_big_data is a FastAPI service rather than a standard KafkaReader/DatabaseWriter service.
    """

    from mon_big_data import dd
    from mon_big_data.config import config

    mocker.patch("mon_big_data.dd.db", new=MockedDBModule())

    # Load test data from JSON file following the same pattern as test_service_runner.
    path = Path(__file__).parent.parent / "olympus_common" / "data" / "mon_big_data.json"
    testdata = json.loads(path.read_text())
    input_: list[dict] = testdata["inputs"]
    output_expected: list[dict] = testdata["outputs"]
    filter_outputs(output_expected, convert_dates=True)

    transformed_data = defaults.transform(input_, config, dd.run, None)

    compare_outputs(transformed_data, output_expected)
